# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Vue 3-based admin dashboard application built with:
- Vue 3 (Composition API)
- Vite 5
- Element Plus UI library
- TypeScript
- Pinia for state management
- Vue Router for routing
- UnoCSS for styling
- Internationalization (vue-i18n)

The project follows a modular architecture with feature-based organization in the `src/views` directory.

## Common Development Commands

### Install Dependencies
```bash
pnpm install
```

### Development
```bash
# Start development server with local environment
pnpm dev

# Start development server with dev environment
pnpm dev-server
```

### Build
```bash
# Local build
pnpm build:local

# Development build
pnpm build:dev

# Test build
pnpm build:test

# Stage build
pnpm build:stage

# Production build
pnpm build:prod
```

### Linting
```bash
# TypeScript check
pnpm ts:check

# ESLint (fixes issues)
pnpm lint:eslint

# Prettier formatting
pnpm lint:format

# Stylelint (fixes issues)
pnpm lint:style
```

### Preview
```bash
# Build and preview locally
pnpm preview
```

## Code Architecture

### Directory Structure
- `src/api` - API service layer
- `src/assets` - Static assets
- `src/components` - Reusable components
- `src/config` - Configuration files
- `src/directives` - Custom Vue directives
- `src/hooks` - Custom Vue composition functions
- `src/layout` - Layout components
- `src/locales` - Internationalization files
- `src/plugins` - Plugin initialization
- `src/router` - Vue Router configuration
- `src/store` - Pinia store modules
- `src/styles` - Global styles
- `src/utils` - Utility functions
- `src/views` - Feature modules (ai, bpm, crm, erp, etc.)

### Key Entry Points
- `src/main.ts` - Application entry point
- `src/App.vue` - Root component
- `src/router/index.ts` - Router configuration
- `src/store/index.ts` - Store configuration
- `vite.config.ts` - Vite build configuration

### Environment Configuration
The project uses multiple environment files:
- `.env` - Base environment
- `.env.local` - Local development
- `.env.dev` - Development environment
- `.env.test` - Test environment
- `.env.stage` - Staging environment
- `.env.prod` - Production environment

Each environment file defines variables like:
- `VITE_PORT` - Development server port
- `VITE_BASE_PATH` - Base URL for the application
- `VITE_API_BASE_PATH` - API base URL