# 本地开发环境：本地启动所有项目（前端、后端、APP）时使用，不依赖外部环境
NODE_ENV=development

VITE_DEV=true

# 请求路径

# VITE_BASE_URL='http://api.talent.rzhrt.com.cn'

# VITE_BASE_URL='http://************:48080'
VITE_BASE_URL='http://dev.cerebro.rzhrt.com.cn/talent'
VITE_DIFY_API_URL='http://dev.cerebro.rzhrt.com.cn:800/v1'
VITE_DIFY_IMAGE_API_KEY='app-MYEIPVIpz6UTWDXFKusIkhQ2'
VITE_DIFY_Scene_API_KEY='app-M0Sdxl0bu0GyZVN53m6A4qfr'
VITE_DIFY_VIDEO_API_KEY='app-9XLQm2uGncP71VZz5VODIH45'
VITE_DIFY_IMAGE_TO_VIDEO_API_KEY='app-d5pvVElEDALUq2tG1bDWDoAw'
VITE_DIFY_CHAT_API_KEY='app-rHfZbNpfSbR28xWIh0Rbu4BJ'
VITE_DIFY_ORIGINAL_API_KEY='app-fuuwcqSPuVB5oDjNUNVjwoe4'
VITE_DIFY_RECREATION_API_KEY='app-MF5PjT7dolWvUmyScN54f0uN'
# VITE_BASE_URL='http://************:48080'
# VITE_BASE_URL='http://************:48080'

# 文件上传类型：server - 后端上传， client - 前端直连上传，仅支持 S3 服务
VITE_UPLOAD_TYPE=server

# 接口地址
VITE_API_URL=/admin-api

# 是否删除debugger
VITE_DROP_DEBUGGER=true

# 是否删除console.log
VITE_DROP_CONSOLE=false

# 是否sourcemap
VITE_SOURCEMAP=false

# 打包路径
VITE_BASE_PATH=/

# 商城H5会员端域名
VITE_MALL_H5_DOMAIN='http://localhost:3000'

# 验证码的开关
VITE_APP_CAPTCHA_ENABLE=false

# GoView域名
VITE_GOVIEW_URL='http://127.0.0.1:3000'