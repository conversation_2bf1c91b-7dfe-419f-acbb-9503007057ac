import request from '@/config/axios'

export const getWorkflowPage = async (params) => {
  return await request.get({ url: '/ai/workflow/page', params })
}

export const getWorkflow = async (id) => {
  return await request.get({ url: '/ai/workflow/get?id=' + id })
}

export const createWorkflow = async (data) => {
  return await request.post({ url: '/ai/workflow/create', data })
}

export const updateWorkflow = async (data) => {
  return await request.put({ url: '/ai/workflow/update', data })
}

export const deleteWorkflow = async (id) => {
  return await request.delete({ url: '/ai/workflow/delete?id=' + id })
}

export const testWorkflow = async (data) => {
  return await request.post({ url: '/ai/workflow/test', data })
}

// ================ Dify API 封装 ================

/**
 * Dify API 配置接口
 */
export interface DifyConfig {
  apiUrl?: string
  apiKey?: string
}

/**
 * Dify 工作流运行请求参数
 */
export interface DifyWorkflowRunRequest {
  inputs: Record<string, any>
  query?: string
  user: string
  response_mode?: 'blocking' | 'streaming'
  tool_parameters?: Record<string, any>
}

/**
 * Dify 工作流运行响应
 */
export interface DifyWorkflowRunResponse {
  data: {
    id: string
    workflow_id: string
    status: string
    outputs: Record<string, any>
    error?: string
    elapsed_time: number
    total_tokens: number
    created_at: number
  }
  answer?: string
}

/**
 * Dify API 错误响应
 */
export interface DifyErrorResponse {
  code: string
  message: string
  status: number
}

/**
 * Dify API 封装类
 */
export class DifyApi {
  private apiUrl: string
  private apiKey: string

  constructor(config?: DifyConfig) {
    this.apiUrl = config?.apiUrl || import.meta.env.VITE_DIFY_API_URL || '/ai'
    this.apiKey = config?.apiKey || import.meta.env.VITE_DIFY_API_KEY || ''
  }

  /**
   * 运行 Dify 工作流
   * @param params 工作流运行参数
   * @param apiKey 可选的 API Key，如果不传则使用实例配置的 API Key
   * @returns Promise<DifyWorkflowRunResponse>
   */
  async runWorkflow(
    params: DifyWorkflowRunRequest,
    apiKey?: string
  ): Promise<DifyWorkflowRunResponse> {
    const useApiKey = apiKey || this.apiKey
    if (!useApiKey) {
      throw new Error('Dify API Key 未配置，请传入 apiKey 参数或检查环境变量 VITE_DIFY_API_KEY')
    }

    const url = `${this.apiUrl}/workflows/run`

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${useApiKey}`
        },
        body: JSON.stringify(params)
      })

      if (!response.ok) {
        const errorData: DifyErrorResponse = await response.json()
        throw new Error(errorData.message || `API 请求失败: ${response.status}`)
      }

      const result: DifyWorkflowRunResponse = await response.json()
      return result
    } catch (error) {
      console.error('Dify 工作流运行失败:', error)
      throw error
    }
  }

  /**
   * 生成图片 - 基于 Stable Diffusion
   * @param prompt 图片描述
   * @param options 生成选项
   * @param apiKey 可选的 API Key，如果不传则使用实例配置的 API Key
   * @returns Promise<string> 返回图片URL
   */
  async generateImage(
    prompt: string,
    options: {
      ratio?: '9:16' | '16:9' | '1:1'
      user?: string
    } = {},
    apiKey?: string
  ): Promise<string> {
    const { ratio = '9:16', user = 'vue-user-123' } = options

    const imageSize = ratio === '16:9' ? '1024x576' : ratio === '1:1' ? '1024x1024' : '576x1024'

    const params: DifyWorkflowRunRequest = {
      inputs: {
        input: prompt,
        ratio: ratio
      },
      query: prompt,
      user: user,
      response_mode: 'blocking',
      tool_parameters: {
        stable_diffusion_image_size: imageSize,
        stable_diffusion_n: 1
      }
    }

    const result = await this.runWorkflow(params, apiKey)

    if (result.data?.outputs?.img_url) {
      return result.data.outputs.img_url
    } else if (result.answer) {
      throw new Error(`生成失败: ${result.answer}`)
    } else {
      throw new Error('API 返回的数据格式不正确，未能找到图片URL')
    }
  }

  /**
   * 批量生成图片
   * @param prompt 图片描述
   * @param count 生成数量
   * @param options 生成选项
   * @param apiKey 可选的 API Key，如果不传则使用实例配置的 API Key
   * @returns Promise<string[]> 返回图片URL数组
   */
  async generateImages(
    prompt: string,
    count: number,
    options: {
      ratio?: '9:16' | '16:9' | '1:1'
      user?: string
      onProgress?: (index: number, total: number, imageUrl: string) => void
    } = {},
    apiKey?: string
  ): Promise<string[]> {
    const { onProgress } = options
    const imageUrls: string[] = []

    for (let i = 0; i < count; i++) {
      try {
        const imageUrl = await this.generateImage(prompt, options, apiKey)
        imageUrls.push(imageUrl)

        // 调用进度回调
        if (onProgress) {
          onProgress(i + 1, count, imageUrl)
        }
      } catch (error) {
        console.error(`生成第 ${i + 1} 张图片失败:`, error)
        // 继续生成下一张，不中断整个流程
      }
    }

    return imageUrls
  }

  /**
   * 多图片生成视频 - 本地成片功能
   * @param images 图片URL数组或图片文件
   * @param options 生成选项
   * @param apiKey 可选的 API Key，如果不传则使用实例配置的 API Key
   * @returns Promise<string> 返回视频URL
   */
  async generateVideoFromImages(
    images: string[] | File[],
    options: {
      prompt?: string
      ratio?: '9:16' | '16:9' | '1:1'
      duration?: '5s' | '10s'
      user?: string
    } = {},
    apiKey?: string
  ): Promise<string> {
    const {
      prompt = '将这些图片制作成视频',
      ratio = '16:9',
      duration = '5s',
      user = 'vue-user-123'
    } = options

    // 处理图片数据
    let imageData: string[]
    if (images.length === 0) {
      throw new Error('至少需要提供一张图片')
    }

    // 如果是文件对象，需要转换为base64或上传
    if (images[0] instanceof File) {
      // 这里可以根据实际需求处理文件上传或转换
      imageData = await Promise.all(
        (images as File[]).map((file) => this.convertFileToBase64(file))
      )
    } else {
      imageData = images as string[]
    }

    const videoSize = ratio === '16:9' ? '1024x576' : ratio === '1:1' ? '1024x1024' : '576x1024'

    const params: DifyWorkflowRunRequest = {
      inputs: {
        input: prompt,
        images: imageData,
        ratio: ratio,
        duration: duration
      },
      query: prompt,
      user: user,
      response_mode: 'blocking',
      tool_parameters: {
        video_generation_ratio: videoSize,
        video_generation_duration: duration,
        video_generation_n: 1,
        image_to_video_images: imageData
      }
    }

    try {
      const result = await this.runWorkflow(params, apiKey)

      if (
        result.data?.outputs?.json &&
        Array.isArray(result.data.outputs.json) &&
        result.data.outputs.json.length > 0
      ) {
        return result.data.outputs.json[0].url
      } else if (result.data?.outputs?.video_url) {
        return result.data.outputs.video_url
      } else if (result.answer) {
        throw new Error(`视频生成失败: ${result.answer}`)
      } else {
        throw new Error('API 返回的数据格式不正确，未能找到视频URL。')
      }
    } catch (error) {
      console.error('多图片生成视频失败:', error)
      throw error
    }
  }

  /**
   * 将文件转换为base64
   * @param file 文件对象
   * @returns Promise<string> base64字符串
   */
  private async convertFileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => {
        const result = reader.result as string
        resolve(result)
      }
      reader.onerror = () => {
        reject(new Error('文件读取失败'))
      }
      reader.readAsDataURL(file)
    })
  }

  /**
   * 更新配置
   * @param config 新的配置
   */
  updateConfig(config: DifyConfig): void {
    if (config.apiUrl) this.apiUrl = config.apiUrl
    if (config.apiKey) this.apiKey = config.apiKey
  }

  /**
   * 静态方法：使用指定的 API Key 运行工作流
   * @param params 工作流运行参数
   * @param apiKey API Key
   * @param apiUrl 可选的 API URL，默认使用环境变量
   * @returns Promise<DifyWorkflowRunResponse>
   */
  static async runWorkflowWithKey(
    params: DifyWorkflowRunRequest,
    apiKey: string,
    apiUrl?: string
  ): Promise<DifyWorkflowRunResponse> {
    const instance = new DifyApi({ apiUrl, apiKey })
    return instance.runWorkflow(params)
  }

  /**
   * 静态方法：使用指定的 API Key 生成图片
   * @param prompt 图片描述
   * @param apiKey API Key
   * @param options 生成选项
   * @param apiUrl 可选的 API URL，默认使用环境变量
   * @returns Promise<string> 返回图片URL
   */
  static async generateImageWithKey(
    prompt: string,
    apiKey: string,
    options: {
      ratio?: '9:16' | '16:9' | '1:1'
      user?: string
    } = {},
    apiUrl?: string
  ): Promise<string> {
    const instance = new DifyApi({ apiUrl, apiKey })
    return instance.generateImage(prompt, options)
  }

  /**
   * 静态方法：使用指定的 API Key 批量生成图片
   * @param prompt 图片描述
   * @param count 生成数量
   * @param apiKey API Key
   * @param options 生成选项
   * @param apiUrl 可选的 API URL，默认使用环境变量
   * @returns Promise<string[]> 返回图片URL数组
   */
  static async generateImagesWithKey(
    prompt: string,
    count: number,
    apiKey: string,
    options: {
      ratio?: '9:16' | '16:9' | '1:1'
      user?: string
      onProgress?: (index: number, total: number, imageUrl: string) => void
    } = {},
    apiUrl?: string
  ): Promise<string[]> {
    const instance = new DifyApi({ apiUrl, apiKey })
    return instance.generateImages(prompt, count, options)
  }

  /**
   * 静态方法：使用指定的 API Key 多图片生成视频
   * @param images 图片URL数组或图片文件
   * @param apiKey API Key
   * @param options 生成选项
   * @param apiUrl 可选的 API URL，默认使用环境变量
   * @returns Promise<string> 返回视频URL
   */
  static async generateVideoFromImagesWithKey(
    images: string[] | File[],
    apiKey: string,
    options: {
      prompt?: string
      ratio?: '9:16' | '16:9' | '1:1'
      duration?: '5s' | '10s'
      user?: string
    } = {},
    apiUrl?: string
  ): Promise<string> {
    const instance = new DifyApi({ apiUrl, apiKey })
    return instance.generateVideoFromImages(images, options)
  }
}

/**
 * 默认的 Dify API 实例
 */
export const difyApi = new DifyApi()
