<template>
  <div id="recreation-generator-page" class="text-white">
    <el-row :gutter="16" class="h-full">
      <!-- 左侧内容区域 -->
      <el-col :span="17">
        <div class="left-content">
          <!-- 上半部分：表单设置 -->
          <div class="settings-panel text-white rounded-2">
            <div class="panel-content">
              <!-- 页面标题 -->
              <div class="page-header">
                <h1>AI 文案生成</h1>
                <span @click="handleClear" class="linear-text">清除</span>
              </div>

              <!-- 从数据预览中提取的文案 -->
              <div class="section">
                <label class="section-title">从数据预览中提取的文案</label>
                <div class="input-wrapper">
                  <textarea
                    v-model="formData.originalContent"
                    placeholder="粘贴您想要二创的原始文案内容..."
                    class="original-content-textarea"
                    rows="6"
                  />
                </div>
              </div>

              <!-- 发布渠道选择 -->
              <div class="section">
                <label class="section-title">发布渠道</label>
                <div class="channel-tabs">
                  <button
                    v-for="channel in channels"
                    :key="channel.key"
                    :class="['channel-btn', { active: activeChannel === channel.key }]"
                    @click="activeChannel = channel.key"
                  >
                    <Icon :icon="channel.icon" class="channel-icon" />
                    {{ channel.label }}
                  </button>
                </div>
              </div>

              <!-- 文案类型选择 -->
              <div class="section">
                <label class="section-title">文案类型</label>
                <div class="content-type-tabs">
                  <button
                    v-for="type in contentTypes"
                    :key="type.key"
                    :class="['type-btn', { active: activeContentType === type.key }]"
                    @click="activeContentType = type.key"
                  >
                    <Icon :icon="type.icon" class="type-icon" />
                    {{ type.label }}
                  </button>
                </div>
              </div>
            </div>

            <!-- 生成按钮 -->
            <GenerateButton
              @click="handleGenerate"
              :loading="isGenerating"
              text="立即生成"
              :cost="3"
              :loading-text="'生成中...'"
            />
          </div>

          <!-- 下半部分：AI二创内容展示 -->
          <div v-if="generatedContent" class="result-panel">
            <!-- AI 二创标题和操作按钮 -->
            <div class="content-header">
              <h2>AI 二创</h2>
              <div class="content-actions">
                <button @click="handleManage" class="action-btn">
                  <Icon icon="ep:user" />
                  人员管理
                </button>
                <button @click="handleCopy" class="action-btn">复制</button>
              </div>
            </div>
            <div class="placeholder-text">
              以下是为您生成的营销内容，您可点击上方"立即生成"按钮再次生成
            </div>

            <!-- 生成的内容 -->
            <div class="generated-content">
              <div class="content-display-text">
                <div class="content-text">
                  <vue-markdown :source="generatedContent.content" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-col>
      <!-- 右侧AI聊天组件 -->
      <el-col :span="7">
        <div class="chat-panel">
          <AiChatWidget
            title="AI设计师"
            welcome-message="Hi，我是你的AI设计师，让我们开始今天的创作吧！"
            :suggestions="aiSuggestions"
            placeholder="在这里输入问题"
            @message-sent="handleMessageSent"
            @message-received="handleMessageReceived"
            @error="handleChatError"
          />
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import AiChatWidget from '@/components/AiChatWidget/index.vue'
import GenerateButton from '@/components/GenerateButton/index.vue'
import { generateRecreationContent, type RecreationContentRequest } from '@/api/ai/workflow'
import VueMarkdown from 'vue-markdown-render'

defineOptions({ name: 'CreationRecreation' })

// 发布渠道数据
const channels = ref([
  { key: '抖音', label: '抖音', icon: 'ep:video-camera' },
  { key: '小红书', label: '小红书', icon: 'ep:picture' }
])

// 文案类型数据
const contentTypes = ref([
  { key: '引流型', label: '引流型', icon: 'ep:promotion' },
  { key: '广告型', label: '广告型', icon: 'ep:sell' }
])

// 当前选中的渠道
const activeChannel = ref('抖音')

// 当前选中的文案类型
const activeContentType = ref('引流型')

// 表单数据
const formData = reactive({
  originalContent: ''
})

// 生成状态
const isGenerating = ref(false)

// 生成的内容
const generatedContent = ref<{
  title: string
  content: string
} | null>(null)

// AI建议问题
const aiSuggestions = ref(['帮我提写营销文案', '科技公司品牌logo设计', '游戏图标设计'])

// 生成文案
const handleGenerate = async () => {
  if (isGenerating.value) return

  // 验证必填字段
  if (!formData.originalContent.trim()) {
    ElMessage.warning('请输入原始文案内容')
    return
  }

  isGenerating.value = true

  try {
    // 构建API请求参数
    const requestParams: RecreationContentRequest = {
      platform: activeChannel.value,
      contentType: activeContentType.value,
      originalContent: formData.originalContent.trim()
    }

    // 调用AI二创文案生成API
    const result = await generateRecreationContent(requestParams)

    // 设置生成的内容
    generatedContent.value = {
      title: result.title,
      content: result.content
    }

    ElMessage.success('文案生成成功！')
  } catch (error) {
    console.error('生成失败:', error)
    ElMessage.error(error instanceof Error ? error.message : '生成失败，请重试')
  } finally {
    isGenerating.value = false
  }
}

// 复制内容
const handleCopy = async () => {
  if (!generatedContent.value) return

  try {
    const textToCopy = `${generatedContent.value.title}\n\n${generatedContent.value.content}`
    await navigator.clipboard.writeText(textToCopy)
    ElMessage.success('内容已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

// 清除表单
const handleClear = () => {
  formData.originalContent = ''
  generatedContent.value = null
  ElMessage.success('已清除所有内容')
}

// 人员管理
const handleManage = () => {
  ElMessage.info('人员管理功能开发中')
}

// AI聊天事件处理
const handleMessageSent = (message: string) => {
  console.log('消息已发送:', message)
}

const handleMessageReceived = (message: string) => {
  console.log('收到回复:', message)
}

const handleChatError = (error: Error) => {
  console.error('聊天错误:', error)
  ElMessage.error(`聊天出错: ${error.message}`)
}
</script>

<style scoped>
:root {
  --primary-bg: #1a1625;
  --secondary-bg: #2a253a;
  --panel-bg: #211e30;
  --text-color: #e0e0e0;
  --text-secondary-color: #a09cb0;
  --accent-color: #6c5ce7;
  --accent-hover-color: #5a4bd7;
  --border-color: #3a364f;
  --input-bg: #3a364f;
}

#recreation-generator-page {
  height: calc(100vh - 165px);
  padding: 0;
}

/* 左侧内容区域 */
.left-content {
  display: flex;
  height: 100%;
  flex-direction: column;
  gap: 16px;
}

/* 左侧设置面板 */
.settings-panel {
  display: flex;
  padding: 16px;
  background: rgb(255 255 255 / 5%);
  border-radius: 8px;
  flex-direction: column;
}

.panel-content {
  flex-grow: 1;
}

/* 页面标题 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-color);
}

/* 区域样式 */
.section {
  margin-bottom: 32px;
}

.section-title {
  display: block;
  margin-bottom: 16px;
  font-size: 14px;
  font-weight: 600;
  letter-spacing: 0.5px;
  color: var(--text-color);
}

/* 原始文案输入框 */
.original-content-textarea {
  width: 100%;
  padding: 16px;
  font-size: 14px;
  line-height: 1.6;
  color: var(--text-color);
  background: transparent;
  border: 1px solid rgb(255 255 255 / 15%);
  border-radius: 8px;
  resize: vertical;
  min-height: 120px;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
}

.original-content-textarea:focus {
  outline: none;
  border-color: #6c5ce7;
  box-shadow: 0 0 0 3px rgb(108 92 231 / 10%);
}

.original-content-textarea::placeholder {
  color: var(--text-secondary-color);
  opacity: 0.7;
}

/* 发布渠道按钮 */
.channel-tabs {
  display: flex;
  gap: 8px;
}

.channel-btn {
  display: flex;
  padding: 8px 16px;
  font-size: 14px;
  color: var(--text-secondary-color);
  cursor: pointer;
  background-color: var(--input-bg);
  border: 1px solid var(--border-color);
  border-radius: 20px;
  transition: all 0.3s;
  align-items: center;
  gap: 6px;
}

.channel-btn.active {
  color: white;
  background: linear-gradient(90deg, #675dff 0%, #bf78e6 100%);
  border: none;
}

.channel-btn:not(.active):hover {
  color: white;
  background: linear-gradient(90deg, #675dff 0%, #bf78e6 100%);
  border: none;
}

.channel-icon {
  font-size: 16px;
}

/* 文案类型按钮 */
.content-type-tabs {
  display: flex;
  gap: 8px;
}

.type-btn {
  display: flex;
  padding: 8px 16px;
  font-size: 14px;
  color: var(--text-secondary-color);
  cursor: pointer;
  background-color: var(--input-bg);
  border: 1px solid var(--border-color);
  border-radius: 20px;
  transition: all 0.3s;
  align-items: center;
  gap: 6px;
}

.type-btn.active {
  color: white;
  background: linear-gradient(90deg, #675dff 0%, #bf78e6 100%);
  border: none;
}

.type-btn:not(.active):hover {
  color: white;
  background: linear-gradient(90deg, #675dff 0%, #bf78e6 100%);
  border: none;
}

.type-icon {
  font-size: 16px;
}

/* 结果面板 */
.result-panel {
  padding: 16px;
  background-color: rgb(255 255 255 / 5%);
  border-radius: 8px;
  flex-grow: 1;
}

.placeholder-text {
  margin-bottom: 16px;
  font-size: 14px;
  color: #aeb9e1;
  text-align: center;
}

/* 内容头部 */
.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.content-header h2 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-color);
}

.content-actions {
  display: flex;
  gap: 12px;
}

.action-btn {
  display: flex;
  padding: 6px 12px;
  font-size: 14px;
  color: var(--accent-color);
  cursor: pointer;
  background: transparent;
  border: 1px solid var(--accent-color);
  border-radius: 6px;
  transition: all 0.3s ease;
  align-items: center;
  gap: 6px;
}

.action-btn:hover {
  color: white;
  background: var(--accent-color);
}

/* 生成的内容 */
.generated-content {
  margin-top: 16px;
}

.content-display-text {
  padding: 16px;
  font-size: 14px;
  line-height: 1.6;
  color: var(--text-color);
  background: rgb(255 255 255 / 3%);
  border: 1px solid rgb(255 255 255 / 10%);
  border-radius: 8px;
}

.content-text {
  line-height: 1.8;
  color: var(--text-color);
  white-space: pre-wrap;
}

/* 聊天面板 */
.chat-panel {
  height: 100%;
}
</style>
