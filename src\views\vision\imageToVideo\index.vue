<template>
  <div id="image-to-video-page">
    <div class="settings-panel text-white rounded-2">
      <div class="panel-content">
        <div class="section">
          <label class="section-title">输入/上传内容成片</label>

          <!-- 文案输入 -->
          <div class="input-wrapper">
            <span class="mb-3">文案</span>
            <textarea
              v-model="formData.prompt"
              placeholder="输入你的想法，然后添加图片"
              class="form-textarea"
            ></textarea>
          </div>

          <!-- 图片上传区域 -->
          <div class="input-wrapper">
            <span class="mb-3">图片</span>

            <!-- 选择素材按钮 -->
            <div class="upload-button-container">
              <button class="upload-button" @click="triggerFileInput">
                <Icon :size="16" icon="ep:plus" />
                <span>选择素材</span>
              </button>
              <input
                ref="fileInput"
                type="file"
                multiple
                accept="image/*"
                @change="handleFileSelect"
                style="display: none"
              />
            </div>

            <!-- 图片展示区域 -->
            <div
              v-if="selectedImages.length > 0"
              class="images-display-area"
              @drop="handleDrop"
              @dragover.prevent
              @dragenter.prevent
            >
              <div class="images-grid">
                <div v-for="(image, index) in selectedImages" :key="index" class="image-item">
                  <div class="image-wrapper">
                    <img :src="image.preview" :alt="`图片 ${index + 1}`" />
                    <div class="image-number">{{ index + 1 }}</div>
                    <button @click.stop="removeImage(index)" class="remove-btn">
                      <Icon :size="12" icon="ep:close" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="section">
          <label class="section-title">参数设置</label>
          <div class="param-row">
            <span>视频比例</span>
            <el-radio-group v-model="formData.aspectRatio" class="custom-radio-group">
              <el-radio value="9:16" class="custom-radio">9:16</el-radio>
              <el-radio value="16:9" class="custom-radio">16:9</el-radio>
            </el-radio-group>
          </div>
          <div class="param-row">
            <span>生成时长</span>
            <el-radio-group v-model="formData.duration" class="custom-radio-group">
              <el-radio value="5s" class="custom-radio">5S</el-radio>
              <el-radio value="10s" class="custom-radio">10S</el-radio>
            </el-radio-group>
          </div>
        </div>
      </div>

      <button
        @click="handleGenerate"
        :disabled="isLoading || isGenerating || selectedImages.length === 0"
        class="generate-btn"
      >
        <span v-if="!isLoading && !isGenerating">立即生成 + {{ selectedImages.length }}</span>
        <span v-else>生成中...</span>
      </button>
    </div>

    <div class="result-panel" :class="{ 'with-history': showHistoryPanel }">
      <div v-if="error" class="error-state">
        <p>出错了：{{ error }}</p>
      </div>
      <div v-else-if="currentDisplayVideo || isGenerating" class="video-display">
        <div v-if="currentDisplayVideo" class="current-video-wrapper">
          <video
            :src="currentDisplayVideo.url || currentDisplayVideo"
            controls
            preload="metadata"
            class="current-video"
          >
            您的浏览器不支持视频播放
          </video>
        </div>
        <div v-else-if="isGenerating" class="generating-placeholder">
          <div class="placeholder-content">
            <div class="spinner"></div>
            <p>正在生成视频...</p>
          </div>
        </div>
      </div>
      <div v-else class="initial-state">
        <img src="@/assets/imgs/vision/frame.png" alt="请在左侧输入描述并设置参数" />
        <p>告诉我你的想法，简单一"点"即可实现</p>
      </div>

      <!-- 显示历史面板按钮 -->
      <button
        v-if="historyVideosList.length > 0"
        @click="showHistoryPanel = true"
        class="history-toggle-btn"
      >
        视频列表 ({{ historyVideosList.length }})
      </button>
    </div>

    <!-- 历史视频列表面板 -->
    <div class="history-panel" :class="{ show: showHistoryPanel }">
      <div class="history-header">
        <h3>视频列表</h3>
        <div class="history-controls">
          <button @click="scrollHistory('up')" class="scroll-btn" :disabled="historyScrollTop <= 0">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M7 14l5-5 5 5z" />
            </svg>
          </button>
          <button
            @click="scrollHistory('down')"
            class="scroll-btn"
            :disabled="historyScrollTop >= maxScrollTop"
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M7 10l5 5 5-5z" />
            </svg>
          </button>
          <button @click="showHistoryPanel = false" class="close-btn">×</button>
        </div>
      </div>
      <div class="history-content" ref="historyContainer" @scroll="updateScrollState">
        <div
          v-for="(video, index) in historyVideosList"
          :key="index"
          class="history-video-wrapper"
          :class="{
            active:
              currentDisplayVideo &&
              (video.url || video) === (currentDisplayVideo.url || currentDisplayVideo)
          }"
          @click="selectHistoryVideo(video)"
        >
          <video
            :src="video.url || video"
            :alt="`历史视频 ${index + 1}`"
            muted
            preload="metadata"
          ></video>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, nextTick, onMounted, watch } from 'vue'
import { difyApi } from '@/api/ai/workflow'

// 表单响应式数据
const formData = ref({
  prompt: '',
  aspectRatio: '16:9',
  duration: '5s'
})

// 图片相关状态
const selectedImages = ref([])
const fileInput = ref(null)

// 其他状态
const isLoading = ref(false)
const isGenerating = ref(false)
const error = ref(null)
const currentDisplayVideo = ref(null)

// 历史视频列表相关状态
const historyVideosList = ref([])
const historyContainer = ref(null)
const historyScrollTop = ref(0)
const maxScrollTop = ref(0)
const showHistoryPanel = ref(false)

// 图片上传相关方法
const triggerFileInput = () => {
  if (fileInput.value) {
    fileInput.value.click()
  }
}

const handleFileSelect = (event) => {
  const files = Array.from(event.target.files)
  addImages(files)
  // 清空input，允许重复选择同一文件
  event.target.value = ''
}

const handleDrop = (event) => {
  event.preventDefault()
  const files = Array.from(event.dataTransfer.files).filter((file) =>
    file.type.startsWith('image/')
  )
  addImages(files)
}

const addImages = (files) => {
  files.forEach((file) => {
    if (file.type.startsWith('image/')) {
      const reader = new FileReader()
      reader.onload = (e) => {
        selectedImages.value.push({
          file: file,
          preview: e.target.result,
          name: file.name
        })
      }
      reader.readAsDataURL(file)
    }
  })
}

const removeImage = (index) => {
  selectedImages.value.splice(index, 1)
}

// 主生成函数
const handleGenerate = async () => {
  if (selectedImages.value.length === 0) {
    alert('请至少选择一张图片！')
    return
  }

  isLoading.value = true
  isGenerating.value = true
  error.value = null
  currentDisplayVideo.value = null

  try {
    // 准备图片文件数组
    const imageFiles = selectedImages.value.map((img) => img.file)

    // 使用封装的 difyApi 生成视频
    const videoUrl = await difyApi.generateVideoFromImages(
      imageFiles,
      {
        prompt: formData.value.prompt || '将这些图片制作成视频',
        ratio: formData.value.aspectRatio,
        duration: formData.value.duration,
        user: 'vue-user-123'
      },
      // 可以根据需要使用不同的 API Key
      import.meta.env.VITE_DIFY_VIDEO_API_KEY || import.meta.env.VITE_DIFY_API_KEY
    )

    // 创建视频数据对象
    const videoData = {
      url: videoUrl,
      prompt: formData.value.prompt || '将这些图片制作成视频',
      timestamp: new Date().toISOString(),
      imageCount: selectedImages.value.length
    }

    // 设置为当前显示的视频
    currentDisplayVideo.value = videoData

    // 添加到历史列表
    historyVideosList.value.push(videoData)

    // 更新滚动状态
    await nextTick()
    updateScrollState()

    console.log('视频生成成功:', videoUrl)
  } catch (err) {
    error.value = err.message
    console.error('视频生成失败:', err)
  } finally {
    isLoading.value = false
    isGenerating.value = false
  }
}

// 历史视频列表相关方法
const scrollHistory = (direction) => {
  if (!historyContainer.value) return

  const scrollAmount = 120
  if (direction === 'up') {
    historyScrollTop.value = Math.max(0, historyScrollTop.value - scrollAmount)
  } else {
    historyScrollTop.value = Math.min(maxScrollTop.value, historyScrollTop.value + scrollAmount)
  }

  historyContainer.value.scrollTop = historyScrollTop.value
}

const updateScrollState = () => {
  if (!historyContainer.value) return

  const container = historyContainer.value
  historyScrollTop.value = container.scrollTop
  maxScrollTop.value = container.scrollHeight - container.clientHeight
}

const selectHistoryVideo = (video) => {
  currentDisplayVideo.value = video
}

// 监听历史列表变化，更新滚动状态
watch(
  historyVideosList,
  async () => {
    await nextTick()
    updateScrollState()
  },
  { deep: true }
)

// 组件挂载后初始化滚动状态
onMounted(() => {
  updateScrollState()
})
</script>

<style>
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

:root {
  --primary-bg: #1a1625;
  --secondary-bg: #2a253a;
  --panel-bg: #211e30;
  --text-color: #e0e0e0;
  --text-secondary-color: #a09cb0;
  --accent-color: #6c5ce7;
  --accent-hover-color: #5a4bd7;
  --border-color: #3a364f;
  --input-bg: #3a364f;
}

#image-to-video-page {
  position: relative;
  display: flex;
  width: 100%;
  height: calc(100vh - 165px);
}

/* 左侧面板 */
.settings-panel {
  display: flex;
  width: 50%;
  padding: 16px;
  background: rgb(255 255 255 / 5%);
  flex-direction: column;
}

.panel-content {
  flex-grow: 1;
}

/* 区域样式 */
.section {
  margin-bottom: 32px;
}

.section-title {
  display: block;
  margin-bottom: 4px;
  font-size: 14px;
  font-weight: 600;
  letter-spacing: 0.5px;
  color: var(--text-color);
}

.input-wrapper {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-bottom: 12px;
}

.input-wrapper span {
  width: 50px;
  padding-top: 8px;
  font-size: 14px;
  color: var(--text-secondary-color);
}

.form-textarea {
  width: 100%;
  min-height: 80px;
  padding: 12px 16px;
  font-size: 14px;
  color: var(--text-color);
  background: transparent;
  border: 1px solid rgb(255 255 255 / 15%);
  border-radius: 10px;
  outline: none;
  transition: all 0.3s ease;
  resize: vertical;
  flex-grow: 1;
  backdrop-filter: blur(5px);
}

.form-textarea:focus {
  background: rgb(255 255 255 / 12%);
  border-color: var(--accent-color);
  box-shadow: 0 0 0 3px rgb(108 92 231 / 20%);
}

.form-textarea::placeholder {
  color: var(--text-secondary-color);
  opacity: 0.7;
}

/* 选择素材按钮样式 */
.upload-button-container {
  margin-bottom: 16px;
}

.upload-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  font-size: 14px;
  color: var(--accent-color);
  cursor: pointer;
  background: rgb(108 92 231 / 10%);
  border: 2px dashed var(--accent-color);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.upload-button:hover {
  background: rgb(108 92 231 / 20%);
  border-color: var(--accent-color);
  transform: translateY(-1px);
}

/* 图片展示区域样式 */
.images-display-area {
  width: 100%;
  padding: 16px;
  background: transparent;
  border: 2px dashed rgb(255 255 255 / 15%);
  border-radius: 10px;
  transition: all 0.3s ease;
}

.images-display-area:hover {
  background: rgb(108 92 231 / 5%);
  border-color: var(--accent-color);
}

/* 图片网格样式 */
.images-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12px;
  width: 100%;
}

.image-item {
  position: relative;
  width: 100%;
  height: 100px;
  overflow: hidden;
  border-radius: 8px;
}

.image-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  transition: all 0.3s ease;
}

.image-wrapper:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgb(108 92 231 / 30%);
}

.image-wrapper img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

/* 图片编号 */
.image-number {
  position: absolute;
  top: 6px;
  left: 6px;
  z-index: 2;
  display: flex;
  width: 24px;
  height: 24px;
  font-size: 12px;
  font-weight: bold;
  color: white;
  background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 100%);
  border-radius: 50%;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgb(0 0 0 / 20%);
}

.remove-btn {
  position: absolute;
  top: -4px;
  right: -4px;
  z-index: 3;
  display: flex;
  width: 20px;
  height: 20px;
  color: white;
  cursor: pointer;
  background: #ff4757;
  border: none;
  border-radius: 50%;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.remove-btn:hover {
  background: #ff3838;
  transform: scale(1.1);
}

.param-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.param-row > span {
  color: var(--text-secondary-color);
}

/* Element Plus Radio 自定义样式 */
.custom-radio-group {
  display: flex;
  gap: 8px;
}

.custom-radio {
  margin-right: 0 !important;
}

.custom-radio :deep(.el-radio__input) {
  display: none;
}

.custom-radio :deep(.el-radio__label) {
  position: relative;
  padding: 8px 16px;
  font-size: 14px;
  color: var(--text-secondary-color);
  cursor: pointer;
  background-color: var(--input-bg);
  border: 1px solid var(--border-color);
  border-radius: 20px;
  transition: all 0.3s;
}

.custom-radio.is-checked :deep(.el-radio__label) {
  position: relative;
  color: white;
  background: linear-gradient(90deg, #675dff 0%, #bf78e6 100%);
  border: none;
  border-radius: 20px;
}

.custom-radio.is-checked :deep(.el-radio__label)::before {
  position: absolute;
  inset: -5px;
  z-index: -1;
  background: linear-gradient(90deg, #675dff 0%, #bf78e6 100%);
  border-radius: 25px;
  content: '';
}

.custom-radio:not(.is-checked) :deep(.el-radio__label):hover {
  position: relative;
  color: white;
  background: linear-gradient(90deg, #675dff 0%, #bf78e6 100%);
  border: none;
}

.custom-radio:not(.is-checked) :deep(.el-radio__label):hover::before {
  position: absolute;
  inset: -2px;
  z-index: -1;
  background: linear-gradient(90deg, #675dff 0%, #bf78e6 100%);
  border-radius: 22px;
  content: '';
}

.generate-btn {
  position: relative;
  width: 100%;
  padding: 16px 24px;
  overflow: hidden;
  font-size: 16px;
  font-weight: 600;
  letter-spacing: 0.5px;
  color: white;
  cursor: pointer;
  background: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-hover-color) 100%);
  border: none;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgb(108 92 231 / 30%);
  transition: all 0.3s ease;
}

.generate-btn::before {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgb(255 255 255 / 20%), transparent);
  content: '';
  transition: left 0.5s;
}

.generate-btn:hover::before {
  left: 100%;
}

.generate-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgb(108 92 231 / 40%);
}

.generate-btn:active {
  transform: translateY(0);
}

.generate-btn:disabled {
  cursor: not-allowed;
  background: linear-gradient(135deg, #555 0%, #444 100%);
  transform: none;
  box-shadow: none;
}

/* 右侧结果区 */
.result-panel {
  display: flex;
  padding: 40px;
  margin-left: 16px;
  overflow-y: auto;
  background-color: rgb(255 255 255 / 5%);
  border-radius: 8px;
  flex-grow: 1;
  align-items: center;
  justify-content: center;
}

.initial-state p,
.error-state p {
  font-size: 18px;
  color: var(--text-secondary-color);
}

.error-state p {
  color: #ff6b6b;
}

/* 视频显示区域 */
.video-display {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.current-video-wrapper {
  position: relative;
  max-width: 90%;
  max-height: 90%;
  overflow: hidden;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgb(0 0 0 / 30%);
}

.current-video {
  display: block;
  width: 100%;
  height: 100%;
}

/* 生成中的占位符 */
.generating-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.placeholder-content {
  color: var(--text-secondary-color);
  text-align: center;
}

.placeholder-content .spinner {
  margin: 0 auto 16px;
}

.placeholder-content p {
  margin: 0;
  font-size: 16px;
}

/* 加载动画 */
.spinner {
  width: 50px;
  height: 50px;
  margin: 0 auto 20px;
  border: 4px solid var(--border-color);
  border-top-color: var(--accent-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* 历史视频面板 */
.history-panel {
  position: fixed;
  top: 0;
  right: -200px;
  z-index: 1000;
  display: flex;
  width: 200px;
  height: 100vh;
  background-color: var(--panel-bg);
  border-left: 1px solid var(--border-color);
  flex-direction: column;
  transition: right 0.3s ease;
}

.history-panel.show {
  right: 0;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid var(--border-color);
}

.history-header h3 {
  margin: 0;
  font-size: 14px;
  font-weight: bold;
  color: var(--text-color);
}

.history-controls {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.scroll-btn {
  display: flex;
  width: 24px;
  height: 24px;
  color: var(--text-secondary-color);
  cursor: pointer;
  background: var(--input-bg);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  transition: all 0.2s;
  align-items: center;
  justify-content: center;
}

.scroll-btn:hover:not(:disabled) {
  color: white;
  background: var(--accent-color);
}

.scroll-btn:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.history-content {
  display: flex;
  padding: 8px;
  overflow-y: auto;
  flex: 1;
  flex-direction: column;
  gap: 8px;
}

.history-content::-webkit-scrollbar {
  width: 4px;
}

.history-content::-webkit-scrollbar-track {
  background: var(--input-bg);
}

.history-content::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 2px;
}

.history-content::-webkit-scrollbar-thumb:hover {
  background: var(--accent-color);
}

.history-video-wrapper {
  position: relative;
  width: 100%;
  padding-top: 100%; /* 1:1 Aspect Ratio */
  overflow: hidden;
  cursor: pointer;
  background-color: var(--secondary-bg);
  border-radius: 6px;
  transition:
    transform 0.2s,
    box-shadow 0.2s;
}

.history-video-wrapper:hover {
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgb(108 92 231 / 30%);
}

.history-video-wrapper.active {
  border: 3px solid #675dff;
  box-shadow: 0 0 0 2px rgb(103 93 255 / 30%);
}

.history-video-wrapper video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 调整主内容区域，为历史面板留出空间 */
.result-panel {
  position: relative;
  transition: margin-right 0.3s ease;
}

/* 当显示历史面板时，调整主内容区域 */
.result-panel.with-history {
  margin-right: 200px;
}

/* 历史面板切换按钮 */
.history-toggle-btn {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 5;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-color);
  cursor: pointer;
  background: var(--panel-bg);
  border: 1px solid var(--border-color);
  border-radius: 20px;
  transition: all 0.3s ease;
}

.history-toggle-btn:hover {
  background: var(--accent-color);
  border-color: var(--accent-color);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgb(108 92 231 / 30%);
}

/* 关闭按钮样式 */
.close-btn {
  display: flex;
  width: 24px;
  height: 24px;
  font-size: 16px;
  color: var(--text-color);
  cursor: pointer;
  background: var(--secondary-bg);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  transition: all 0.2s ease;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: var(--accent-color);
  border-color: var(--accent-color);
}
</style>
