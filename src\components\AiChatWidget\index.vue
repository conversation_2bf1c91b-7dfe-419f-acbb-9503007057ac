<template>
  <div class="ai-chat-widget" :style="widgetStyle">
    <!-- 聊天头部 -->
    <!-- <div class="chat-header" v-if="showHeader">
      <div class="header-title">
        <Icon icon="ep:chat-dot-round" class="header-icon" />
        <span>{{ title }}</span>
      </div>
      <div class="header-actions">
        <el-button
          v-if="enableClear"
          type="text"
          size="small"
          @click="clearMessages"
          :disabled="messages.length === 0"
        >
          <Icon icon="ep:delete" />
        </el-button>
      </div>
    </div> -->

    <!-- 消息列表容器 -->
    <div class="chat-messages" ref="messagesContainer">
      <!-- 欢迎消息 -->
      <div v-if="messages.length === 0 && welcomeMessage" class="welcome-section">
        <div class="welcome-content">
          <h2 class="welcome-title"> Hi,我是你的 <span class="ai-highlight">AI设计师</span> </h2>
          <p class="welcome-subtitle">让我们开始今天的创作吧！</p>
        </div>

        <!-- 建议问题 -->
        <div v-if="suggestions.length > 0" class="suggestions">
          <div
            v-for="(suggestion, index) in suggestions"
            :key="index"
            class="suggestion-item"
            @click="sendSuggestion(suggestion)"
          >
            {{ suggestion }}
          </div>
        </div>
      </div>

      <!-- 消息列表 -->
      <div v-for="(message, index) in messages" :key="index" class="message-item">
        <!-- 用户消息 -->
        <div v-if="message.type === 'user'" class="user-message">
          <div class="message-content user-content">
            {{ message.content }}
          </div>
          <div class="user-avatar">
            <Icon icon="ep:user" />
          </div>
        </div>

        <!-- AI消息 -->
        <div v-else class="ai-message">
          <div class="ai-avatar">
            <Icon icon="ep:robot" />
          </div>
          <div class="message-content ai-content">
            <div class="message-text" v-html="formatMessage(message.content)"></div>
            <div v-if="message.isStreaming" class="typing-indicator">
              <span></span>
              <span></span>
              <span></span>
            </div>
          </div>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="isLoading && !isStreaming" class="loading-message">
        <div class="ai-avatar">
          <Icon icon="ep:robot" />
        </div>
        <div class="message-content ai-content">
          <div class="typing-indicator">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>
      </div>
    </div>

    <!-- 输入区域 -->
    <div class="chat-input">
      <div class="input-container">
        <el-input
          v-model="inputMessage"
          type="textarea"
          :placeholder="placeholder"
          :autosize="{ minRows: 1, maxRows: 4 }"
          @keydown="handleKeydown"
          :disabled="isLoading"
          resize="none"
        />
        <el-button
          type="primary"
          :disabled="!inputMessage.trim() || isLoading"
          @click="sendMessage"
          class="send-button"
        >
          <Icon v-if="!isLoading" icon="ep:position" />
          <Icon v-else icon="ep:loading" class="loading-icon" />
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { difyApi, DifyApi } from '@/api/ai/workflow'

// 消息接口
interface ChatMessage {
  id: string
  type: 'user' | 'ai'
  content: string
  timestamp: number
  isStreaming?: boolean
}

// Props 定义
interface Props {
  // API配置
  apiKey?: string
  apiUrl?: string

  // 外观配置
  height?: string
  width?: string
  theme?: 'dark' | 'light'

  // 功能配置
  placeholder?: string
  maxMessages?: number
  enableHistory?: boolean
  enableClear?: boolean
  showHeader?: boolean
  title?: string

  // 初始配置
  welcomeMessage?: string
  suggestions?: string[]

  // 高级配置
  user?: string
  systemPrompt?: string
}

const props = withDefaults(defineProps<Props>(), {
  height: 'calc(100vh - 160px)',
  width: '100%',
  theme: 'dark',
  placeholder: '在这里输入问题...',
  maxMessages: 50,
  enableHistory: true,
  enableClear: true,
  showHeader: true,
  title: 'AI助手',
  welcomeMessage: 'Hi,我是你的AI设计师',
  suggestions: () => ['帮我写一段代码', '解释一个概念', '给我一些建议'],
  user: 'vue-user-123'
})

// Events 定义
const emit = defineEmits<{
  'message-sent': [message: string]
  'message-received': [message: string]
  error: [error: Error]
  'stream-start': []
  'stream-end': []
}>()

// 响应式数据
const inputMessage = ref('')
const messages = ref<ChatMessage[]>([])
const isLoading = ref(false)
const isStreaming = ref(false)
const messagesContainer = ref<HTMLElement>()
const conversationId = ref<string>()

// 计算属性 - 组件样式
const widgetStyle = computed(() => ({
  height: props.height,
  width: props.width,
  minHeight: props.height,
  maxHeight: props.height,
  overflow: 'hidden'
}))

// 发送消息
const sendMessage = async () => {
  const message = inputMessage.value.trim()
  if (!message || isLoading.value) return

  // 添加用户消息
  const userMessage: ChatMessage = {
    id: generateId(),
    type: 'user',
    content: message,
    timestamp: Date.now()
  }

  messages.value.push(userMessage)
  inputMessage.value = ''
  emit('message-sent', message)

  // 滚动到底部
  await nextTick()
  scrollToBottom()

  // 发送到AI
  await sendToAI(message)
}

// 发送建议问题
const sendSuggestion = (suggestion: string) => {
  inputMessage.value = suggestion
  sendMessage()
}

// 发送到AI
const sendToAI = async (message: string) => {
  isLoading.value = true
  isStreaming.value = true
  emit('stream-start')

  // 添加AI消息占位符
  const aiMessage: ChatMessage = {
    id: generateId(),
    type: 'ai',
    content: '',
    timestamp: Date.now(),
    isStreaming: true
  }

  messages.value.push(aiMessage)
  await nextTick()
  scrollToBottom()

  try {
    const apiInstance = props.apiKey
      ? new DifyApi({ apiKey: props.apiKey, apiUrl: props.apiUrl })
      : difyApi

    // 打印当前conversation_id状态
    console.log('发送消息，当前conversation_id:', conversationId.value)

    await apiInstance.streamChat(
      message,
      {
        user: props.user,
        conversationId: conversationId.value,
        systemPrompt: props.systemPrompt
      },
      props.apiKey,
      // onMessage
      (content: string, isComplete: boolean) => {
        const lastMessage = messages.value[messages.value.length - 1]
        if (lastMessage && lastMessage.type === 'ai') {
          lastMessage.content += content
          scrollToBottom()
        }
      },
      // onError
      (error: Error) => {
        console.error('AI聊天错误:', error)
        ElMessage.error(`聊天出错: ${error.message}`)
        emit('error', error)

        // 移除失败的AI消息
        if (messages.value.length > 0 && messages.value[messages.value.length - 1].type === 'ai') {
          messages.value.pop()
        }
      },
      // onComplete
      (newConversationId?: string) => {
        // 保存conversation_id
        if (newConversationId && !conversationId.value) {
          conversationId.value = newConversationId
          console.log('保存conversation_id:', newConversationId)
        }

        const lastMessage = messages.value[messages.value.length - 1]
        if (lastMessage && lastMessage.type === 'ai') {
          lastMessage.isStreaming = false
          emit('message-received', lastMessage.content)
        }

        isLoading.value = false
        isStreaming.value = false
        emit('stream-end')

        // 限制消息数量
        if (messages.value.length > props.maxMessages) {
          messages.value = messages.value.slice(-props.maxMessages)
        }
      }
    )
  } catch (error) {
    console.error('发送消息失败:', error)
    ElMessage.error(`发送失败: ${(error as Error).message}`)
    emit('error', error as Error)

    isLoading.value = false
    isStreaming.value = false
    emit('stream-end')

    // 移除失败的AI消息
    if (messages.value.length > 0 && messages.value[messages.value.length - 1].type === 'ai') {
      messages.value.pop()
    }
  }
}

// 处理键盘事件
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault()
    sendMessage()
  }
}

// 清空消息
const clearMessages = () => {
  messages.value = []
  conversationId.value = undefined
}

// 滚动到底部
const scrollToBottom = () => {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
  }
}

// 格式化消息（支持简单的markdown）
const formatMessage = (content: string) => {
  return content
    .replace(/\n/g, '<br>')
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    .replace(/`(.*?)`/g, '<code>$1</code>')
}

// 生成唯一ID
const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

// 组件挂载时的初始化
onMounted(() => {
  // 如果启用历史记录，可以在这里加载历史消息
  if (props.enableHistory) {
    // TODO: 实现历史记录加载
  }
})
</script>

<style lang="scss" scoped>
/* 动画 */
@keyframes typing {
  0%,
  60%,
  100% {
    opacity: 0.4;
    transform: translateY(0);
  }

  30% {
    opacity: 1;
    transform: translateY(-10px);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.ai-chat-widget {
  position: relative;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: linear-gradient(
    270deg,
    #250d41 0%,
    #1b0d36 17.56%,
    #130c2c 39.46%,
    #0f0b2b 58.45%,
    #0c0b29 75.97%,
    #0b0b33 100%
  );
  border: 1px solid rgb(255 255 255 / 10%);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgb(0 0 0 / 30%);
  box-sizing: border-box;

  // 聊天头部
  .chat-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    background: rgb(255 255 255 / 5%);
    border-bottom: 1px solid rgb(255 255 255 / 10%);
    flex-shrink: 0; // 防止头部被压缩

    .header-title {
      display: flex;
      font-size: 16px;
      font-weight: 600;
      color: #fff;
      align-items: center;
      gap: 8px;

      .header-icon {
        font-size: 20px;
        color: #6366f1;
      }
    }

    .header-actions {
      .el-button {
        color: rgb(255 255 255 / 70%);

        &:hover {
          color: #fff;
          background: rgb(255 255 255 / 10%);
        }
      }
    }
  }

  // 消息列表
  .chat-messages {
    display: flex;
    height: 0; // 强制flex子项计算高度
    min-height: 0; // 重要：允许flex子项收缩
    padding: 20px;
    overflow: hidden auto;
    flex: 1;
    flex-direction: column;
    gap: 16px;

    // 自定义滚动条
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: rgb(255 255 255 / 10%);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgb(255 255 255 / 30%);
      border-radius: 3px;

      &:hover {
        background: rgb(255 255 255 / 50%);
      }
    }

    // 欢迎区域
    .welcome-section {
      display: flex;
      min-height: 300px;
      padding: 60px 20px;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 32px;
      flex-shrink: 0; // 防止欢迎区域被压缩

      .welcome-content {
        max-width: 600px;
        text-align: center;

        .welcome-title {
          margin: 0 0 16px;
          font-size: 28px;
          font-weight: 500;
          line-height: 1.3;
          color: #fff;

          .ai-highlight {
            position: relative;
            color: #6366f1;

            &::after {
              position: absolute;
              right: 0;
              bottom: -2px;
              left: 0;
              height: 3px;
              background: linear-gradient(90deg, #6366f1, #8b5cf6);
              border-radius: 2px;
              content: '';
            }
          }
        }

        .welcome-subtitle {
          margin: 0;
          font-size: 16px;
          line-height: 1.5;
          color: rgb(255 255 255 / 80%);
        }
      }

      .suggestions {
        display: flex;
        flex-direction: column;
        gap: 12px;
        width: 100%;
        max-width: 500px;

        .suggestion-item {
          padding: 14px 20px;
          font-size: 15px;
          color: rgb(255 255 255 / 90%);
          text-align: center;
          cursor: pointer;
          background: rgb(255 255 255 / 8%);
          border: 1px solid rgb(255 255 255 / 15%);
          border-radius: 12px;
          transition: all 0.3s ease;
          backdrop-filter: blur(10px);

          &:hover {
            color: #fff;
            background: rgb(255 255 255 / 15%);
            border-color: rgb(255 255 255 / 30%);
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgb(0 0 0 / 20%);
          }
        }
      }
    }

    // 消息项
    .message-item {
      display: flex;
      flex-direction: column;
      gap: 8px;
      flex-shrink: 0; // 防止消息项被压缩

      // 用户消息
      .user-message {
        display: flex;
        align-items: flex-end;
        gap: 12px;
        justify-content: flex-end;

        .user-content {
          max-width: 70%;
          color: white;
          background: linear-gradient(135deg, #6366f1, #8b5cf6);
          border-radius: 16px 16px 4px;
        }

        .user-avatar {
          display: flex;
          width: 32px;
          height: 32px;
          font-size: 14px;
          color: white;
          background: rgb(255 255 255 / 10%);
          border-radius: 50%;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;
        }
      }

      // AI消息
      .ai-message {
        display: flex;
        align-items: flex-start;
        gap: 12px;

        .ai-avatar {
          display: flex;
          width: 32px;
          height: 32px;
          font-size: 14px;
          color: white;
          background: linear-gradient(135deg, #6366f1, #8b5cf6);
          border-radius: 50%;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;
        }

        .ai-content {
          max-width: 70%;
          color: #fff;
          background: rgb(255 255 255 / 10%);
          border: 1px solid rgb(255 255 255 / 20%);
          border-radius: 16px 16px 16px 4px;
        }
      }

      // 加载消息
      .loading-message {
        display: flex;
        align-items: flex-start;
        gap: 12px;

        .ai-avatar {
          display: flex;
          width: 32px;
          height: 32px;
          font-size: 14px;
          color: white;
          background: linear-gradient(135deg, #6366f1, #8b5cf6);
          border-radius: 50%;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;
        }

        .ai-content {
          color: #fff;
          background: rgb(255 255 255 / 10%);
          border: 1px solid rgb(255 255 255 / 20%);
          border-radius: 16px 16px 16px 4px;
        }
      }

      // 消息内容
      .message-content {
        padding: 12px 16px;
        font-size: 14px;
        line-height: 1.5;
        word-wrap: break-word;

        .message-text {
          :deep(strong) {
            font-weight: 600;
          }

          :deep(em) {
            font-style: italic;
          }

          :deep(code) {
            padding: 2px 6px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            background: rgb(0 0 0 / 20%);
            border-radius: 4px;
          }
        }
      }
    }

    // 打字指示器
    .typing-indicator {
      display: flex;
      align-items: center;
      gap: 4px;
      padding: 8px 0;

      span {
        width: 6px;
        height: 6px;
        background: rgb(255 255 255 / 60%);
        border-radius: 50%;
        animation: typing 1.4s infinite ease-in-out;

        &:nth-child(1) {
          animation-delay: 0s;
        }

        &:nth-child(2) {
          animation-delay: 0.2s;
        }

        &:nth-child(3) {
          animation-delay: 0.4s;
        }
      }
    }
  }

  // 输入区域
  .chat-input {
    padding: 16px 20px;
    background: rgb(255 255 255 / 5%);
    border-top: 1px solid rgb(255 255 255 / 10%);
    flex-shrink: 0; // 防止输入区域被压缩

    .input-container {
      display: flex;
      align-items: flex-end;
      gap: 12px;

      :deep(.el-textarea) {
        flex: 1;

        .el-textarea__inner {
          padding: 12px 16px;
          font-size: 14px;
          line-height: 1.4;
          color: #fff;
          background: rgb(255 255 255 / 10%);
          border: 1px solid rgb(255 255 255 / 20%);
          border-radius: 12px;
          resize: none;

          &::placeholder {
            color: rgb(255 255 255 / 50%);
          }

          &:focus {
            border-color: #6366f1;
            box-shadow: 0 0 0 2px rgb(99 102 241 / 20%);
          }
        }
      }

      .send-button {
        display: flex;
        width: 40px;
        height: 40px;
        color: white;
        background: linear-gradient(135deg, #6366f1, #8b5cf6);
        border: none;
        border-radius: 50%;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;

        &:hover:not(:disabled) {
          transform: scale(1.05);
          box-shadow: 0 4px 12px rgb(99 102 241 / 40%);
        }

        &:disabled {
          cursor: not-allowed;
          opacity: 0.5;
        }

        .loading-icon {
          animation: spin 1s linear infinite;
        }
      }
    }
  }
}

// 浅色主题
.ai-chat-widget[data-theme='light'] {
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border-color: #e2e8f0;

  .chat-header {
    background: rgb(0 0 0 / 2%);
    border-bottom-color: #e2e8f0;

    .header-title {
      color: #1e293b;
    }

    .header-actions .el-button {
      color: #64748b;

      &:hover {
        color: #1e293b;
        background: rgb(0 0 0 / 5%);
      }
    }
  }

  .chat-messages {
    .welcome-section {
      .welcome-message .welcome-text {
        color: #1e293b;
        background: #fff;
        border-color: #e2e8f0;
      }

      .suggestions .suggestion-item {
        color: #64748b;
        background: #fff;
        border-color: #e2e8f0;

        &:hover {
          color: #1e293b;
          background: #f8fafc;
          border-color: #cbd5e1;
        }
      }
    }

    .message-item {
      .ai-message .ai-content {
        color: #1e293b;
        background: #fff;
        border-color: #e2e8f0;
      }

      .loading-message .ai-content {
        color: #1e293b;
        background: #fff;
        border-color: #e2e8f0;
      }
    }

    .typing-indicator span {
      background: #64748b;
    }
  }

  .chat-input {
    background: rgb(0 0 0 / 2%);
    border-top-color: #e2e8f0;

    .input-container :deep(.el-textarea .el-textarea__inner) {
      color: #1e293b;
      background: #fff;
      border-color: #e2e8f0;

      &::placeholder {
        color: #94a3b8;
      }

      &:focus {
        border-color: #6366f1;
      }
    }
  }
}
</style>
