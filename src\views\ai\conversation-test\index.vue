<template>
  <div class="conversation-test">
    <ContentWrap title="Conversation ID 测试">
      <div class="test-description">
        <h3>测试目标</h3>
        <p>验证AI聊天组件是否能正确保存和使用conversation_id来维持对话连续性</p>
        
        <h3>测试步骤</h3>
        <ol>
          <li>发送第一条消息，系统应该获取并保存conversation_id</li>
          <li>发送后续消息，系统应该使用已保存的conversation_id</li>
          <li>查看控制台日志，确认conversation_id的传递</li>
          <li>清空对话，conversation_id应该被重置</li>
        </ol>

        <h3>预期结果</h3>
        <ul>
          <li>第一次对话后，conversation_id应该被保存</li>
          <li>后续对话应该使用相同的conversation_id</li>
          <li>AI应该能记住之前的对话内容</li>
        </ul>
      </div>

      <div class="test-container">
        <div class="chat-section">
          <h3>聊天测试区域</h3>
          <div class="chat-widget-container">
            <AiChatWidget 
              height="500px"
              title="Conversation ID 测试"
              welcome-message="请发送消息测试conversation_id功能"
              :suggestions="testSuggestions"
              placeholder="输入测试消息..."
              @message-sent="onMessageSent"
              @message-received="onMessageReceived"
              @error="onError"
              @stream-start="onStreamStart"
              @stream-end="onStreamEnd"
            />
          </div>
        </div>

        <div class="info-section">
          <h3>测试信息</h3>
          <div class="info-panel">
            <div class="info-item">
              <label>当前状态:</label>
              <span :class="statusClass">{{ currentStatus }}</span>
            </div>
            <div class="info-item">
              <label>消息计数:</label>
              <span>{{ messageCount }}</span>
            </div>
            <div class="info-item">
              <label>流式状态:</label>
              <span>{{ isStreaming ? '进行中' : '空闲' }}</span>
            </div>
          </div>

          <h3>测试日志</h3>
          <div class="log-container">
            <div 
              v-for="(log, index) in logs" 
              :key="index"
              class="log-item"
              :class="log.type"
            >
              <span class="log-time">{{ log.time }}</span>
              <span class="log-type">{{ log.type.toUpperCase() }}</span>
              <span class="log-message">{{ log.message }}</span>
            </div>
          </div>
          
          <div class="log-actions">
            <el-button @click="clearLogs" size="small">清空日志</el-button>
            <el-button @click="exportLogs" size="small" type="primary">导出日志</el-button>
          </div>
        </div>
      </div>

      <div class="test-suggestions">
        <h3>建议测试消息</h3>
        <div class="suggestion-grid">
          <el-button 
            v-for="(suggestion, index) in advancedSuggestions" 
            :key="index"
            @click="sendTestMessage(suggestion)"
            size="small"
            type="info"
          >
            {{ suggestion }}
          </el-button>
        </div>
      </div>
    </ContentWrap>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'

defineOptions({ name: 'ConversationTest' })

// 测试数据
const testSuggestions = ref([
  '你好，我是第一次对话',
  '请记住我的名字叫张三',
  '我刚才说我叫什么名字？'
])

const advancedSuggestions = ref([
  '你好，请记住我喜欢蓝色',
  '我刚才说我喜欢什么颜色？',
  '请帮我写一首关于春天的诗',
  '你还记得我之前的要求吗？',
  '总结一下我们的对话内容'
])

// 状态管理
const currentStatus = ref('等待中')
const messageCount = ref(0)
const isStreaming = ref(false)

const statusClass = computed(() => {
  switch (currentStatus.value) {
    case '发送中': return 'status-sending'
    case '接收中': return 'status-receiving'
    case '完成': return 'status-complete'
    case '错误': return 'status-error'
    default: return 'status-waiting'
  }
})

// 日志管理
const logs = ref<Array<{
  time: string
  type: 'info' | 'success' | 'warning' | 'error'
  message: string
}>>([])

const addLog = (type: 'info' | 'success' | 'warning' | 'error', message: string) => {
  const time = new Date().toLocaleTimeString()
  logs.value.unshift({ time, type, message })
  
  if (logs.value.length > 50) {
    logs.value = logs.value.slice(0, 50)
  }
}

// 事件处理
const onMessageSent = (message: string) => {
  messageCount.value++
  currentStatus.value = '发送中'
  addLog('info', `发送消息 #${messageCount.value}: ${message}`)
  ElMessage.info('消息已发送')
}

const onMessageReceived = (message: string) => {
  currentStatus.value = '完成'
  addLog('success', `收到回复: ${message.substring(0, 50)}${message.length > 50 ? '...' : ''}`)
  ElMessage.success('收到AI回复')
}

const onError = (error: Error) => {
  currentStatus.value = '错误'
  addLog('error', `发生错误: ${error.message}`)
  ElMessage.error(`错误: ${error.message}`)
}

const onStreamStart = () => {
  isStreaming.value = true
  currentStatus.value = '接收中'
  addLog('info', '开始接收流式回复')
}

const onStreamEnd = () => {
  isStreaming.value = false
  currentStatus.value = '完成'
  addLog('success', '流式回复结束')
}

// 工具方法
const sendTestMessage = (message: string) => {
  // 这里可以通过组件引用直接发送消息
  addLog('info', `准备发送测试消息: ${message}`)
}

const clearLogs = () => {
  logs.value = []
  addLog('info', '日志已清空')
}

const exportLogs = () => {
  const logText = logs.value.map(log => 
    `[${log.time}] ${log.type.toUpperCase()}: ${log.message}`
  ).join('\n')
  
  const blob = new Blob([logText], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `conversation-test-logs-${new Date().toISOString().slice(0, 10)}.txt`
  a.click()
  URL.revokeObjectURL(url)
  
  addLog('success', '日志已导出')
}

// 初始化
addLog('info', 'Conversation ID 测试页面已加载')
addLog('warning', '请打开浏览器控制台查看详细的conversation_id日志')
</script>

<style lang="scss" scoped>
.conversation-test {
  .test-description {
    margin-bottom: 24px;
    padding: 20px;
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;

    h3 {
      margin: 0 0 12px 0;
      color: #1e293b;
      font-size: 16px;
      font-weight: 600;
    }

    p, li {
      color: #475569;
      line-height: 1.6;
    }

    ol, ul {
      margin: 8px 0;
      padding-left: 20px;
    }
  }

  .test-container {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 24px;
    margin-bottom: 24px;

    .chat-section {
      h3 {
        margin: 0 0 16px 0;
        color: #1e293b;
        font-size: 18px;
        font-weight: 600;
      }

      .chat-widget-container {
        border: 2px solid #e2e8f0;
        border-radius: 8px;
        overflow: hidden;
      }
    }

    .info-section {
      h3 {
        margin: 0 0 12px 0;
        color: #1e293b;
        font-size: 16px;
        font-weight: 600;
      }

      .info-panel {
        padding: 16px;
        background: #f1f5f9;
        border-radius: 6px;
        margin-bottom: 20px;

        .info-item {
          display: flex;
          justify-content: space-between;
          margin-bottom: 8px;

          &:last-child {
            margin-bottom: 0;
          }

          label {
            font-weight: 500;
            color: #64748b;
          }

          span {
            font-weight: 600;

            &.status-waiting { color: #64748b; }
            &.status-sending { color: #f59e0b; }
            &.status-receiving { color: #3b82f6; }
            &.status-complete { color: #10b981; }
            &.status-error { color: #ef4444; }
          }
        }
      }

      .log-container {
        max-height: 300px;
        overflow-y: auto;
        border: 1px solid #e2e8f0;
        border-radius: 6px;
        background: #ffffff;
        margin-bottom: 12px;

        .log-item {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 6px 12px;
          border-bottom: 1px solid #f1f5f9;
          font-size: 12px;

          &:last-child {
            border-bottom: none;
          }

          &.info { border-left: 3px solid #3b82f6; }
          &.success { border-left: 3px solid #10b981; }
          &.warning { border-left: 3px solid #f59e0b; }
          &.error { border-left: 3px solid #ef4444; }

          .log-time {
            color: #64748b;
            font-family: monospace;
            min-width: 70px;
          }

          .log-type {
            color: #1e293b;
            font-weight: 600;
            min-width: 60px;
          }

          .log-message {
            color: #475569;
            flex: 1;
            word-break: break-all;
          }
        }
      }

      .log-actions {
        display: flex;
        gap: 8px;
      }
    }
  }

  .test-suggestions {
    h3 {
      margin: 0 0 16px 0;
      color: #1e293b;
      font-size: 16px;
      font-weight: 600;
    }

    .suggestion-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 8px;
    }
  }
}

@media (max-width: 768px) {
  .conversation-test .test-container {
    grid-template-columns: 1fr;
  }
}
</style>
